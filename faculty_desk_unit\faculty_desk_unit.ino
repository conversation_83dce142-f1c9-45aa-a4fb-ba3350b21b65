#include <WiFi.h>
#include <PubSubClient.h>  // MQTT client
#include <BLEDevice.h>
#include <BLEScan.h>
#include <BLEAdvertisedDevice.h>
#include <SPI.h>
#include <Adafruit_GFX.h>    // Core graphics library
#include <Adafruit_ST7789.h> // Hardware-specific library for ST7789
#include <time.h>
#include <WiFiUdp.h>         // For NTP client
#include <NTPClient.h>       // NTP client library
#include "config.h"          // Include configuration file

// Current Date/Time and User
const char* current_date_time = "2025-05-02 09:46:02";
const char* current_user = FACULTY_NAME;

// WiFi credentials
const char* ssid = WIFI_SSID;
const char* password = WIFI_PASSWORD;

// MQTT Broker settings
const char* mqtt_server = MQTT_SERVER;
const int mqtt_port = MQTT_PORT;
char mqtt_topic_messages[50];
char mqtt_topic_status[50];
char mqtt_topic_mac_status[50];
char mqtt_topic_legacy_messages[50];
char mqtt_topic_legacy_status[50];
char mqtt_client_id[50];

// Faculty Beacon MAC Address - nRF51822 BLE Beacon
// This ESP32 unit is configured to detect only its assigned faculty member's beacon
// The MAC address is defined in config.h as FACULTY_BEACON_MAC
// IMPORTANT: Update FACULTY_BEACON_MAC in config.h with the actual nRF51822 beacon MAC address
const char* assignedFacultyBeaconMac = FACULTY_BEACON_MAC;

// Status update timing
unsigned long lastStatusUpdate = 0;

// MAC Address Detection Variables
BLEScan* pBLEScan = nullptr;
bool facultyPresent = false;
bool oldFacultyPresent = false;
unsigned long lastMacDetectionTime = 0;
unsigned long lastBleScanTime = 0;
int macDetectionCount = 0;
int macAbsenceCount = 0;
String detectedFacultyMac = "";
String lastDetectedMac = "";

// TFT Display pins for ST7789
#define TFT_CS    5
#define TFT_DC    21
#define TFT_RST   22
#define TFT_MOSI  23
#define TFT_SCLK  18

// Initialize the ST7789 display with hardware SPI
Adafruit_ST7789 tft = Adafruit_ST7789(TFT_CS, TFT_DC, TFT_MOSI, TFT_SCLK, TFT_RST);

// NTP Time Synchronization Settings
const char* ntpServer1 = NTP_SERVER_1;
const char* ntpServer2 = NTP_SERVER_2;
const char* ntpServer3 = NTP_SERVER_3;
const long  gmtOffset_sec = TIMEZONE_OFFSET_HOURS * 3600;  // Convert hours to seconds
const int   daylightOffset_sec = 0;    // Philippines doesn't use daylight saving time

// NTP Client setup
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, ntpServer1, gmtOffset_sec, 60000); // Update every 60 seconds

// Time synchronization variables
bool ntpSyncSuccessful = false;
bool ntpInitialized = false;
unsigned long lastNtpSync = 0;
unsigned long ntpSyncInterval = NTP_SYNC_INTERVAL_HOURS * 3600000; // Convert hours to milliseconds
unsigned long ntpSyncRetryInterval = NTP_RETRY_INTERVAL_MINUTES * 60000; // Convert minutes to milliseconds
int ntpSyncAttempts = 0;
const int maxNtpSyncAttempts = NTP_MAX_RETRY_ATTEMPTS;

// Variables
WiFiClient espClient;
PubSubClient mqttClient(espClient);
char timeStringBuff[50];
char dateStringBuff[50];
String lastMessage = "";
unsigned long lastDisplayUpdate = 0;
unsigned long lastTimeUpdate = 0;

// National University Philippines Color Scheme
#define NU_BLUE      0x0015      // Dark blue (navy) - Primary color
#define NU_GOLD      0xFE60      // Gold/Yellow - Secondary color
#define NU_DARKBLUE  0x000B      // Darker blue for contrasts
#define NU_LIGHTGOLD 0xF710      // Lighter gold for highlights
#define ST77XX_WHITE 0xFFFF      // White for text
#define ST77XX_BLACK 0x0000      // Black for backgrounds
#define ST77XX_GREEN 0x07E0      // Green for success indicators
#define ST77XX_RED   0xF800      // Red for error indicators
#define ST77XX_ORANGE 0xFD20     // Orange for warning indicators
#define ST77XX_YELLOW 0xFFE0     // Yellow for warning indicators

// Colors for the UI
#define COLOR_BACKGROUND     NU_BLUE         // Changed to blue as primary color
#define COLOR_TEXT           ST77XX_WHITE
#define COLOR_HEADER         NU_DARKBLUE
#define COLOR_MESSAGE_BG     NU_BLUE
#define COLOR_STATUS_GOOD    NU_GOLD
#define COLOR_STATUS_WARNING ST77XX_YELLOW
#define COLOR_STATUS_ERROR   ST77XX_RED
#define COLOR_ACCENT         NU_GOLD
#define COLOR_HIGHLIGHT      NU_LIGHTGOLD

// UI Layout constants - No gaps
#define HEADER_HEIGHT 40
#define STATUS_HEIGHT 20
#define MESSAGE_AREA_TOP HEADER_HEIGHT        // No gap after header
#define MESSAGE_TITLE_HEIGHT 30
#define MESSAGE_TEXT_TOP (MESSAGE_AREA_TOP + MESSAGE_TITLE_HEIGHT)

// Gold accent width
#define ACCENT_WIDTH 5



// Function to process incoming messages and extract content from JSON if needed
String processMessage(String message) {
  // Check if the message is in JSON format (starts with '{')
  if (message.startsWith("{")) {
    Serial.println("Detected JSON message, attempting to extract content");

    // First try to extract the message field
    int messageStart = message.indexOf("\"message\":\"");
    if (messageStart > 0) {
      messageStart += 11; // Length of "message":"
      int messageEnd = message.indexOf("\"", messageStart);
      if (messageEnd > messageStart) {
        String extractedMessage = message.substring(messageStart, messageEnd);
        // Replace escaped quotes and newlines
        extractedMessage.replace("\\\"", "\"");
        extractedMessage.replace("\\n", "\n");
        Serial.print("Extracted message field: ");
        Serial.println(extractedMessage);
        return extractedMessage;
      }
    }

    // If message field not found, try to extract request_message field
    messageStart = message.indexOf("\"request_message\":\"");
    if (messageStart > 0) {
      messageStart += 18; // Length of "request_message":"
      int messageEnd = message.indexOf("\"", messageStart);
      if (messageEnd > messageStart) {
        String extractedMessage = message.substring(messageStart, messageEnd);
        // Replace escaped quotes and newlines
        extractedMessage.replace("\\\"", "\"");
        extractedMessage.replace("\\n", "\n");

        // Try to get student name and course code to format a complete message
        String studentName = "";
        int studentStart = message.indexOf("\"student_name\":\"");
        if (studentStart > 0) {
          studentStart += 16; // Length of "student_name":"
          int studentEnd = message.indexOf("\"", studentStart);
          if (studentEnd > studentStart) {
            studentName = message.substring(studentStart, studentEnd);
          }
        }

        String courseCode = "";
        int courseStart = message.indexOf("\"course_code\":\"");
        if (courseStart > 0) {
          courseStart += 14; // Length of "course_code":"
          int courseEnd = message.indexOf("\"", courseStart);
          if (courseEnd > courseStart) {
            courseCode = message.substring(courseStart, courseEnd);
          }
        }

        // Format a complete message
        String formattedMessage = "";
        if (studentName != "") {
          formattedMessage += "Student: " + studentName + "\n";
        }
        if (courseCode != "") {
          formattedMessage += "Course: " + courseCode + "\n";
        }
        formattedMessage += "Request: " + extractedMessage;

        Serial.print("Formatted message: ");
        Serial.println(formattedMessage);
        return formattedMessage;
      }
    }

    // If no specific message field found, return the whole JSON for debugging
    Serial.println("No message field found in JSON, displaying raw JSON");
    return message;
  }

  // If not JSON, return the original message
  return message;
}

// MAC Address Detection Functions
String normalizeMacAddress(String mac) {
  // Convert MAC address to uppercase and ensure consistent format
  mac.toUpperCase();
  mac.replace("-", ":");
  return mac;
}

bool isFacultyMacAddress(String mac) {
  // Normalize the detected MAC address
  String normalizedMac = normalizeMacAddress(mac);

  // Check against this unit's assigned faculty beacon MAC address
  String assignedMac = normalizeMacAddress(String(assignedFacultyBeaconMac));

  // Only match if this is the assigned faculty member's beacon
  if (normalizedMac.equals(assignedMac)) {
    Serial.print("Matched assigned faculty beacon: ");
    Serial.print(normalizedMac);
    Serial.print(" (Faculty ID: ");
    Serial.print(FACULTY_ID);
    Serial.print(" - ");
    Serial.print(FACULTY_NAME);
    Serial.println(")");
    return true;
  }

  return false;
}

// BLE Scan Callback Class with Enhanced Debugging
class MyAdvertisedDeviceCallbacks: public BLEAdvertisedDeviceCallbacks {
    void onResult(BLEAdvertisedDevice advertisedDevice) {
      String deviceMac = advertisedDevice.getAddress().toString().c_str();
      int rssi = advertisedDevice.getRSSI();
      String deviceName = "";

      // Get device name if available
      if (advertisedDevice.haveName()) {
        deviceName = advertisedDevice.getName().c_str();
      }

      // Log all detected devices for debugging
      Serial.print("BLE Device: ");
      Serial.print(deviceMac);
      Serial.print(" | RSSI: ");
      Serial.print(rssi);
      Serial.print(" dBm | Name: ");
      Serial.print(deviceName.length() > 0 ? deviceName : "Unknown");

      // Show additional device information
      if (advertisedDevice.haveServiceUUID()) {
        Serial.print(" | Service: ");
        Serial.print(advertisedDevice.getServiceUUID().toString().c_str());
      }

      if (advertisedDevice.haveManufacturerData()) {
        Serial.print(" | Mfg Data: Yes");
      }

      Serial.println();

      // Check if this is our target faculty beacon
      bool isTargetBeacon = isFacultyMacAddress(deviceMac);
      bool rssiAcceptable = (rssi > BLE_RSSI_THRESHOLD);

      if (isTargetBeacon) {
        Serial.print("*** TARGET BEACON FOUND: ");
        Serial.print(deviceMac);
        Serial.print(" | RSSI: ");
        Serial.print(rssi);
        Serial.print(" dBm | Threshold: ");
        Serial.print(BLE_RSSI_THRESHOLD);
        Serial.print(" dBm | ");
        Serial.println(rssiAcceptable ? "RSSI OK" : "RSSI TOO WEAK");

        if (rssiAcceptable) {
          // Update detection variables
          detectedFacultyMac = deviceMac;
          lastMacDetectionTime = millis();
          macDetectionCount++;
          macAbsenceCount = 0; // Reset absence counter

          Serial.print("Faculty presence detected: ");
          Serial.print(FACULTY_NAME);
          Serial.print(" (Detection count: ");
          Serial.print(macDetectionCount);
          Serial.println(")");
        } else {
          Serial.println("Target beacon found but RSSI too weak - not counting as detection");
        }
      }
    }
};

void initializeBLEScanner() {
  Serial.println("=== BLE Scanner Initialization ===");
  Serial.println("Initializing BLE Scanner for nRF51822 beacon detection...");
  Serial.print("Assigned Faculty: ");
  Serial.print(FACULTY_NAME);
  Serial.print(" (ID: ");
  Serial.print(FACULTY_ID);
  Serial.println(")");
  Serial.print("Target Beacon MAC: ");
  Serial.println(assignedFacultyBeaconMac);

  // Check if beacon MAC is configured
  if (String(assignedFacultyBeaconMac) == "00:00:00:00:00:00") {
    Serial.println("*** WARNING: Beacon MAC address not configured! ***");
    Serial.println("*** Please update FACULTY_BEACON_MAC in config.h ***");
    Serial.println("*** Detection will not work with default MAC address ***");
    displaySystemStatus("Beacon MAC not configured!");
  }

  Serial.println("BLE Configuration:");
  Serial.print("  Scan Interval: ");
  Serial.print(BLE_SCAN_INTERVAL);
  Serial.println(" ms");
  Serial.print("  Scan Duration: ");
  Serial.print(BLE_SCAN_DURATION);
  Serial.println(" seconds");
  Serial.print("  RSSI Threshold: ");
  Serial.print(BLE_RSSI_THRESHOLD);
  Serial.println(" dBm");
  Serial.print("  Active Scan: ");
  Serial.println(MAC_SCAN_ACTIVE ? "Yes" : "No");
  Serial.print("  Detection Debounce: ");
  Serial.println(MAC_DETECTION_DEBOUNCE);

  // Initialize BLE
  String deviceName = "ConsultEase-Faculty-" + String(FACULTY_ID);
  BLEDevice::init(deviceName.c_str());
  Serial.print("BLE Device Name: ");
  Serial.println(deviceName);

  // Create BLE Scanner with optimized parameters for nRF51822
  pBLEScan = BLEDevice::getScan();
  pBLEScan->setAdvertisedDeviceCallbacks(new MyAdvertisedDeviceCallbacks());
  pBLEScan->setActiveScan(MAC_SCAN_ACTIVE);

  // Optimized scan parameters for nRF51822 beacon detection
  // Interval: 80ms (recommended for beacon scanning)
  // Window: 80ms (100% duty cycle for maximum detection)
  pBLEScan->setInterval(80);
  pBLEScan->setWindow(80);

  Serial.println("BLE Scanner Parameters:");
  Serial.println("  Scan Interval: 80ms (optimized for beacons)");
  Serial.println("  Scan Window: 80ms (100% duty cycle)");
  Serial.println("  Scan Type: " + String(MAC_SCAN_ACTIVE ? "Active" : "Passive"));

  Serial.println("✅ BLE Scanner initialized successfully");
  Serial.println("=== Ready for nRF51822 Beacon Detection ===");
  displaySystemStatus("BLE Scanner ready");
}

void performBLEScan() {
  if (pBLEScan == nullptr) {
    Serial.println("❌ BLE Scanner not initialized");
    return;
  }

  unsigned long currentTime = millis();

  // Only scan at specified intervals
  if (currentTime - lastBleScanTime < BLE_SCAN_INTERVAL) {
    return;
  }

  lastBleScanTime = currentTime;

  Serial.println();
  Serial.println("=== BLE SCAN START ===");
  Serial.print("🔍 Scanning for ");
  Serial.print(FACULTY_NAME);
  Serial.println("'s nRF51822 beacon...");
  Serial.print("Target MAC: ");
  Serial.println(assignedFacultyBeaconMac);
  Serial.print("RSSI Threshold: ");
  Serial.print(BLE_RSSI_THRESHOLD);
  Serial.println(" dBm");

  displaySystemStatus("Scanning for beacon...");

  // Clear previous detection for this scan
  String previousDetectedMac = detectedFacultyMac;
  detectedFacultyMac = "";

  // Record scan start time
  unsigned long scanStartTime = millis();

  // Perform scan with enhanced error handling
  BLEScanResults* foundDevices = nullptr;
  try {
    foundDevices = pBLEScan->start(BLE_SCAN_DURATION, false);
  } catch (const std::exception& e) {
    Serial.print("❌ BLE scan failed with exception: ");
    Serial.println(e.what());
    displaySystemStatus("BLE scan error");
    return;
  }

  unsigned long scanDuration = millis() - scanStartTime;

  // Scan results summary
  Serial.println("--- SCAN RESULTS ---");
  Serial.print("Scan Duration: ");
  Serial.print(scanDuration);
  Serial.println(" ms");

  int totalDevicesFound = 0;
  if (foundDevices) {
    totalDevicesFound = foundDevices->getCount();
  }

  Serial.print("Total BLE devices found: ");
  Serial.println(totalDevicesFound);

  // Check if we detected the assigned faculty member's beacon
  bool currentScanDetected = (detectedFacultyMac.length() > 0);

  if (currentScanDetected) {
    Serial.println("✅ TARGET BEACON DETECTED!");
    Serial.print("   Faculty: ");
    Serial.println(FACULTY_NAME);
    Serial.print("   MAC: ");
    Serial.println(detectedFacultyMac);
    Serial.print("   Detection Count: ");
    Serial.println(macDetectionCount);
    displaySystemStatus(String(FACULTY_NAME) + " detected");
  } else {
    Serial.println("❌ Target beacon NOT detected");
    Serial.print("   Looking for: ");
    Serial.println(assignedFacultyBeaconMac);
    Serial.print("   Absence Count: ");
    Serial.println(macAbsenceCount + 1);
    macAbsenceCount++;

    // Provide troubleshooting hints
    if (String(assignedFacultyBeaconMac) == "00:00:00:00:00:00") {
      Serial.println("   ⚠️  ISSUE: Default MAC address - please configure actual beacon MAC");
    } else if (totalDevicesFound == 0) {
      Serial.println("   ⚠️  ISSUE: No BLE devices found - check beacon is powered and advertising");
    } else {
      Serial.println("   ⚠️  ISSUE: Beacon not in range or MAC address mismatch");
    }

    displaySystemStatus("Beacon not found");
  }

  // Clear scan results to free memory
  pBLEScan->clearResults();

  // Update faculty presence based on debouncing logic
  updateFacultyPresenceStatus();

  // Display scan statistics
  Serial.println("--- SCAN STATISTICS ---");
  Serial.print("Current Faculty Status: ");
  Serial.println(facultyPresent ? "PRESENT" : "ABSENT");
  Serial.print("Detection Count: ");
  Serial.println(macDetectionCount);
  Serial.print("Absence Count: ");
  Serial.println(macAbsenceCount);
  Serial.print("Last Detection: ");
  if (lastMacDetectionTime > 0) {
    Serial.print((currentTime - lastMacDetectionTime) / 1000);
    Serial.println(" seconds ago");
  } else {
    Serial.println("Never");
  }

  Serial.println("=== BLE SCAN END ===");
  Serial.println();
}

void updateFacultyPresenceStatus() {
  bool newFacultyPresent = false;

  // Determine presence based on recent detections and debouncing
  if (macDetectionCount >= MAC_DETECTION_DEBOUNCE) {
    // Faculty has been consistently detected
    newFacultyPresent = true;
  } else if (macAbsenceCount >= MAC_DETECTION_DEBOUNCE) {
    // Faculty has been consistently absent
    newFacultyPresent = false;
  } else {
    // Not enough consistent readings, maintain current state
    newFacultyPresent = facultyPresent;
  }

  // Check for timeout (faculty left without proper detection)
  unsigned long currentTime = millis();
  if (facultyPresent && (currentTime - lastMacDetectionTime > MAC_DETECTION_TIMEOUT)) {
    Serial.println("Faculty presence timeout - marking as absent");
    newFacultyPresent = false;
    macAbsenceCount = MAC_DETECTION_DEBOUNCE; // Force absence state
  }

  // Update status if changed
  if (newFacultyPresent != facultyPresent) {
    oldFacultyPresent = facultyPresent;
    facultyPresent = newFacultyPresent;

    // Reset counters when state changes
    macDetectionCount = 0;
    macAbsenceCount = 0;

    // Update last detected MAC for status reporting
    if (facultyPresent) {
      lastDetectedMac = detectedFacultyMac;
    }

    Serial.print(FACULTY_NAME);
    Serial.print(" presence changed: ");
    Serial.println(facultyPresent ? "PRESENT" : "ABSENT");

    // Publish status change via MQTT
    publishFacultyStatus();
  }
}

void publishFacultyStatus() {
  if (!mqttClient.connected()) {
    Serial.println("MQTT not connected, cannot publish status");
    return;
  }

  const char* status_message;
  String detailed_status;

  if (facultyPresent) {
    status_message = "faculty_present";
    detailed_status = String(FACULTY_NAME) + " detected via beacon: " + lastDetectedMac;

    // Also send legacy format for backward compatibility
    mqttClient.publish(mqtt_topic_status, "keychain_connected");
    mqttClient.publish(mqtt_topic_legacy_status, "keychain_connected");

    Serial.print("Published ");
    Serial.print(FACULTY_NAME);
    Serial.println(" present status");
    displaySystemStatus(String(FACULTY_NAME) + " Present");
  } else {
    status_message = "faculty_absent";
    detailed_status = String(FACULTY_NAME) + " beacon not detected";

    // Also send legacy format for backward compatibility
    mqttClient.publish(mqtt_topic_status, "keychain_disconnected");
    mqttClient.publish(mqtt_topic_legacy_status, "keychain_disconnected");

    Serial.print("Published ");
    Serial.print(FACULTY_NAME);
    Serial.println(" absent status");
    displaySystemStatus(String(FACULTY_NAME) + " Absent");
  }

  // Publish detailed status with MAC address information
  String statusPayload = "{\"status\":\"" + String(status_message) + "\",\"mac\":\"" + lastDetectedMac + "\",\"timestamp\":" + String(millis()) + "}";

  mqttClient.publish(mqtt_topic_mac_status, statusPayload.c_str());

  Serial.print("Published detailed status: ");
  Serial.println(statusPayload);
}

// Function to draw the continuous gold accent bar
void drawGoldAccent() {
  // Draw gold accent that spans the entire height except status bar
  tft.fillRect(0, 0, ACCENT_WIDTH, tft.height() - STATUS_HEIGHT, COLOR_ACCENT);
}

// Centralized UI update function that preserves the gold accent
void updateUIArea(int area, const String &message = "") {
  // Area types:
  // 0 = Full message area
  // 1 = Message title area only
  // 2 = Message content area only
  // 3 = Status bar only

  switch (area) {
    case 0: // Full message area
      // Clear the message area but preserve gold accent
      tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP,
                  tft.width() - ACCENT_WIDTH,
                  tft.height() - MESSAGE_AREA_TOP - STATUS_HEIGHT,
                  COLOR_MESSAGE_BG);

      // Ensure gold accent is intact
      drawGoldAccent();

      // If message provided, display it
      if (message.length() > 0) {
        tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
        tft.setTextSize(2);
        tft.setTextColor(NU_GOLD);
        tft.println(message);
      }
      break;

    case 1: // Message title area only
      // Clear just the title area
      tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP,
                  tft.width() - ACCENT_WIDTH,
                  MESSAGE_TITLE_HEIGHT,
                  COLOR_MESSAGE_BG);

      // Ensure gold accent is intact
      drawGoldAccent();

      // If message provided, display it as title
      if (message.length() > 0) {
        tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
        tft.setTextSize(2);
        tft.setTextColor(NU_GOLD);
        tft.println(message);
      }
      break;

    case 2: // Message content area only
      // Clear just the content area below title
      tft.fillRect(ACCENT_WIDTH, MESSAGE_TEXT_TOP,
                  tft.width() - ACCENT_WIDTH,
                  tft.height() - MESSAGE_TEXT_TOP - STATUS_HEIGHT,
                  COLOR_MESSAGE_BG);

      // Ensure gold accent is intact
      drawGoldAccent();
      break;

    case 3: // Status bar only
      // Update status bar
      displaySystemStatus(message);
      break;
  }
}

// Function to test the full display
void testScreen() {
  // Test pattern to verify the display is working properly
  tft.fillScreen(NU_BLUE);
  delay(500);

  // Draw National University Philippines colors for test
  int sectionHeight = tft.height() / 3;

  // Draw sections with no gaps
  tft.fillRect(0, 0, tft.width(), sectionHeight, NU_DARKBLUE);
  tft.fillRect(0, sectionHeight, tft.width(), sectionHeight, NU_BLUE);
  tft.fillRect(0, 2*sectionHeight, tft.width(), sectionHeight, NU_GOLD);

  // Add continuous gold accent line at left
  drawGoldAccent();

  // Display text
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(2);

  tft.setCursor(ACCENT_WIDTH + 5, 10);
  tft.println("National University");

  tft.setCursor(ACCENT_WIDTH + 5, sectionHeight + 10);
  tft.println("Philippines");

  tft.setTextColor(NU_DARKBLUE);
  tft.setCursor(ACCENT_WIDTH + 5, 2*sectionHeight + 10);
  tft.println("Professor's Desk Unit");

  delay(3000);
  tft.fillScreen(NU_BLUE);
}

// NTP Time Synchronization Functions
void initializeNTP() {
  Serial.println("Initializing NTP client...");
  displaySystemStatus("Initializing NTP...");

  timeClient.begin();
  ntpInitialized = true;

  // Try initial synchronization
  syncTimeWithNTP();
}

bool syncTimeWithNTP() {
  if (!ntpInitialized) {
    Serial.println("NTP not initialized");
    return false;
  }

  Serial.println("Attempting NTP time synchronization...");
  displaySystemStatus("Syncing time...");

  // Try primary NTP server
  bool success = timeClient.update();

  if (!success && ntpSyncAttempts < maxNtpSyncAttempts) {
    Serial.println("Primary NTP server failed, trying alternative servers...");

    // Try alternative servers
    const char* servers[] = {ntpServer2, ntpServer3};
    for (int i = 0; i < 2 && !success; i++) {
      Serial.print("Trying NTP server: ");
      Serial.println(servers[i]);

      // Reinitialize with different server
      timeClient.end();
      timeClient = NTPClient(ntpUDP, servers[i], gmtOffset_sec, 60000);
      timeClient.begin();

      delay(1000); // Give time for initialization
      success = timeClient.update();
    }

    // If alternative servers failed, go back to primary
    if (!success) {
      timeClient.end();
      timeClient = NTPClient(ntpUDP, ntpServer1, gmtOffset_sec, 60000);
      timeClient.begin();
    }
  }

  if (success) {
    // Get the epoch time from NTP
    unsigned long epochTime = timeClient.getEpochTime();

    // Set the system time using the built-in ESP32 time functions
    struct timeval tv;
    tv.tv_sec = epochTime;
    tv.tv_usec = 0;
    settimeofday(&tv, NULL);

    // Also configure timezone for ESP32 internal clock
    setenv("TZ", "PHT-8", 1); // Philippines Time UTC+8
    tzset();

    ntpSyncSuccessful = true;
    lastNtpSync = millis();
    ntpSyncAttempts = 0;

    Serial.println("NTP synchronization successful!");
    Serial.print("Current time: ");
    Serial.println(timeClient.getFormattedTime());

    displaySystemStatus("Time synced successfully");

    // Show sync success indicator on display
    showTimeSyncIndicator(true);

    return true;
  } else {
    ntpSyncSuccessful = false;
    ntpSyncAttempts++;

    Serial.print("NTP synchronization failed (attempt ");
    Serial.print(ntpSyncAttempts);
    Serial.print("/");
    Serial.print(maxNtpSyncAttempts);
    Serial.println(")");

    displaySystemStatus("Time sync failed");

    // Show sync failure indicator on display
    showTimeSyncIndicator(false);

    return false;
  }
}

void showTimeSyncIndicator(bool success) {
  // Show a small indicator in the top-right corner of the header
  int indicatorX = tft.width() - 25;
  int indicatorY = 5;
  int indicatorSize = 8;

  if (success) {
    // Green circle for successful sync
    tft.fillCircle(indicatorX, indicatorY + indicatorSize/2, indicatorSize/2, ST77XX_GREEN);
    tft.drawCircle(indicatorX, indicatorY + indicatorSize/2, indicatorSize/2, COLOR_TEXT);
  } else {
    // Red circle for failed sync
    tft.fillCircle(indicatorX, indicatorY + indicatorSize/2, indicatorSize/2, ST77XX_RED);
    tft.drawCircle(indicatorX, indicatorY + indicatorSize/2, indicatorSize/2, COLOR_TEXT);
  }

  // Show indicator for 3 seconds
  delay(3000);

  // Clear the indicator area and redraw header
  tft.fillRect(indicatorX - indicatorSize, indicatorY, indicatorSize * 2, indicatorSize + 5, COLOR_HEADER);
}

void checkPeriodicTimeSync() {
  unsigned long currentTime = millis();

  // Check if it's time for periodic sync
  if (ntpSyncSuccessful && (currentTime - lastNtpSync > ntpSyncInterval)) {
    Serial.println("Performing periodic NTP synchronization...");
    syncTimeWithNTP();
  }
  // If last sync failed, retry more frequently
  else if (!ntpSyncSuccessful && (currentTime - lastNtpSync > ntpSyncRetryInterval)) {
    Serial.println("Retrying NTP synchronization...");
    syncTimeWithNTP();
  }
}

String getFormattedTime() {
  if (ntpSyncSuccessful && ntpInitialized) {
    // Use NTP synchronized time
    timeClient.update(); // Update to get latest time
    return timeClient.getFormattedTime();
  } else {
    // Fallback to ESP32 internal RTC
    struct tm timeinfo;
    if (getLocalTime(&timeinfo)) {
      char timeStr[10];
      strftime(timeStr, sizeof(timeStr), "%H:%M:%S", &timeinfo);
      return String(timeStr);
    } else {
      // Last resort: use hardcoded time
      String dateTime = String(current_date_time);
      return dateTime.substring(11); // Extract time part
    }
  }
}

String getFormattedDate() {
  if (ntpSyncSuccessful && ntpInitialized) {
    // Use NTP synchronized time
    time_t epochTime = timeClient.getEpochTime();
    struct tm *ptm = gmtime(&epochTime);

    char dateStr[12];
    sprintf(dateStr, "%04d-%02d-%02d",
            ptm->tm_year + 1900,
            ptm->tm_mon + 1,
            ptm->tm_mday);
    return String(dateStr);
  } else {
    // Fallback to ESP32 internal RTC
    struct tm timeinfo;
    if (getLocalTime(&timeinfo)) {
      char dateStr[12];
      strftime(dateStr, sizeof(dateStr), "%Y-%m-%d", &timeinfo);
      return String(dateStr);
    } else {
      // Last resort: use hardcoded date
      String dateTime = String(current_date_time);
      return dateTime.substring(0, 10); // Extract date part
    }
  }
}

// Function to draw the header with time and date
void updateTimeDisplay() {
  // Clear only the header area, preserving the gold accent
  tft.fillRect(ACCENT_WIDTH, 0, tft.width() - ACCENT_WIDTH, HEADER_HEIGHT, COLOR_HEADER);

  // Get current time using NTP-synchronized time
  String timeStr = getFormattedTime();
  String dateStr = getFormattedDate();

  // Convert time from HH:MM:SS to HH:MM for display
  if (timeStr.length() >= 5) {
    timeStr = timeStr.substring(0, 5); // Extract HH:MM
  }

  // Convert date from YYYY-MM-DD to MM/DD/YYYY for display
  if (dateStr.length() >= 10) {
    String year = dateStr.substring(0, 4);
    String month = dateStr.substring(5, 7);
    String day = dateStr.substring(8, 10);
    dateStr = month + "/" + day + "/" + year;
  }

  // Draw time on left
  tft.setTextColor(COLOR_TEXT);
  tft.setTextSize(2);
  tft.setCursor(ACCENT_WIDTH + 5, 10);
  tft.print(timeStr);

  // Draw date on right
  int16_t x1, y1;
  uint16_t w, h;
  tft.getTextBounds(dateStr, 0, 0, &x1, &y1, &w, &h);
  tft.setCursor(tft.width() - w - 10, 10);
  tft.print(dateStr);

  // Show time sync status indicator (small dot in top-right corner)
  int statusX = tft.width() - 15;
  int statusY = 8;

  if (ntpSyncSuccessful) {
    // Green dot for successful NTP sync
    tft.fillCircle(statusX, statusY, 3, ST77XX_GREEN);
  } else {
    // Orange dot for fallback time (no NTP sync)
    tft.fillCircle(statusX, statusY, 3, ST77XX_ORANGE);
  }

  // Ensure gold accent is intact
  drawGoldAccent();
}

// Function to display a new message
void displayMessage(String message) {
  // Use centralized UI update function to clear the full message area
  updateUIArea(0);

  // Display "New Message:" title with gold accent
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 5);
  tft.setTextColor(COLOR_ACCENT);  // Gold color for title
  tft.setTextSize(2);
  tft.println("New Message:");

  // Draw a gold divider line right after the title
  tft.drawFastHLine(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + MESSAGE_TITLE_HEIGHT - 5, tft.width() - ACCENT_WIDTH - 10, COLOR_ACCENT);

  // Display message text with word wrapping
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_TEXT_TOP);
  tft.setTextColor(COLOR_TEXT);  // White text for message
  tft.setTextSize(2);

  // Handle long messages with line wrapping
  int16_t x1, y1;
  uint16_t w, h;
  int maxWidth = tft.width() - ACCENT_WIDTH - 10;
  String line = "";
  int yPos = MESSAGE_TEXT_TOP;

  for (int i = 0; i < message.length(); i++) {
    char c = message.charAt(i);
    line += c;

    tft.getTextBounds(line, 0, 0, &x1, &y1, &w, &h);

    if (w > maxWidth || c == '\n') {
      // Remove the last character if it was due to width
      if (w > maxWidth && c != '\n')
        line = line.substring(0, line.length() - 1);

      tft.setCursor(ACCENT_WIDTH + 5, yPos);
      tft.println(line);

      yPos += h + 2;  // Reduced spacing for more compact layout
      line = (w > maxWidth && c != '\n') ? String(c) : "";
    }
  }

  // Print any remaining text
  if (line.length() > 0) {
    tft.setCursor(ACCENT_WIDTH + 5, yPos);
    tft.println(line);
  }

  lastMessage = message;
}

// Show system status on display
void displaySystemStatus(String status) {
  // Clear the status area at the bottom of the screen
  tft.fillRect(0, tft.height() - STATUS_HEIGHT, tft.width(), STATUS_HEIGHT, NU_DARKBLUE);

  // Display the status text
  tft.setCursor(ACCENT_WIDTH + 5, tft.height() - STATUS_HEIGHT + 5);
  tft.setTextColor(COLOR_STATUS_GOOD);
  tft.setTextSize(1);
  tft.println(status);

  // Draw a gold line above status bar
  tft.drawFastHLine(0, tft.height() - STATUS_HEIGHT, tft.width(), COLOR_ACCENT);
}

// MQTT callback with improved topic handling
void callback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");

  String message = "";
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }

  Serial.println(message);

  // Enhanced debug output
  Serial.println("Message details:");
  Serial.print("Topic: ");
  Serial.println(topic);
  Serial.print("Length: ");
  Serial.println(length);
  Serial.print("Content: ");
  Serial.println(message);

  // Check if this is a system ping message (for keeping connection alive)
  if (strcmp(topic, MQTT_TOPIC_NOTIFICATIONS) == 0 && message.indexOf("ping") >= 0) {
    Serial.println("Received system ping, no need to display");
    return;
  }

  // Process the message based on format
  message = processMessage(message);

  // Display the message on TFT with visual notification
  displayMessage(message);
  displaySystemStatus("New message received");

  // Flash the screen briefly to draw attention to the new message
  for (int i = 0; i < 3; i++) {
    // Flash the header area
    tft.fillRect(ACCENT_WIDTH, 0, tft.width() - ACCENT_WIDTH, HEADER_HEIGHT, COLOR_ACCENT);
    delay(100);
    tft.fillRect(ACCENT_WIDTH, 0, tft.width() - ACCENT_WIDTH, HEADER_HEIGHT, COLOR_HEADER);
    delay(100);
  }

  // Restore the time display
  updateTimeDisplay();
}

// Draw the main UI framework - truly seamless design
void drawUIFramework() {
  // Fill entire screen with the NU blue color
  tft.fillScreen(COLOR_BACKGROUND);

  // Draw the header bar with darker blue - no gaps
  tft.fillRect(ACCENT_WIDTH, 0, tft.width() - ACCENT_WIDTH, HEADER_HEIGHT, COLOR_HEADER);

  // Draw status bar area - seamless with main area
  tft.fillRect(0, tft.height() - STATUS_HEIGHT, tft.width(), STATUS_HEIGHT, NU_DARKBLUE);

  // Add thin gold accent line above status bar
  tft.drawFastHLine(0, tft.height() - STATUS_HEIGHT, tft.width(), COLOR_ACCENT);

  // Draw continuous gold accent at left
  drawGoldAccent();
}

void setup_wifi() {
  // Clear the main content area but preserve the gold accent bar
  tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP, tft.width() - ACCENT_WIDTH, tft.height() - MESSAGE_AREA_TOP - STATUS_HEIGHT, COLOR_BACKGROUND);

  // Ensure gold accent is intact
  drawGoldAccent();

  // Display connecting message
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
  tft.setTextColor(COLOR_TEXT);
  tft.setTextSize(2);
  tft.println("Connecting to WiFi");

  // Add the SSID right beneath
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 40);
  tft.setTextSize(1);
  tft.println(ssid);

  // Connect to WiFi
  delay(10);
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);

  WiFi.begin(ssid, password);

  // Animated connecting indicator with National University colors
  int dots = 0;
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");

    // Clear dot area but preserve gold accent
    tft.fillRect(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 60, tft.width() - ACCENT_WIDTH - 10, 20, COLOR_BACKGROUND);

    // Update dots animation with alternating colors
    tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 60);
    tft.setTextColor(COLOR_TEXT);
    tft.print("Connecting");

    for (int i = 0; i < 6; i++) {
      if (i < dots) {
        // Alternate between blue and gold dots
        tft.setTextColor((i % 2 == 0) ? NU_GOLD : NU_LIGHTGOLD);
        tft.print(".");
      } else {
        tft.print(" ");
      }
    }

    dots = (dots + 1) % 7;
  }

  // Connected - display connection details
  Serial.println("");
  Serial.println("WiFi connected");
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());

  // Clear the connecting message and show success - but preserve gold accent bar
  tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP, tft.width() - ACCENT_WIDTH, tft.height() - MESSAGE_AREA_TOP - STATUS_HEIGHT, COLOR_BACKGROUND);

  // Ensure gold accent is intact
  drawGoldAccent();

  // Show connected message
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
  tft.setTextSize(2);
  tft.setTextColor(NU_GOLD);
  tft.println("WiFi Connected");

  // Show network details
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 50);
  tft.setTextSize(1);
  tft.setTextColor(COLOR_TEXT);
  tft.print("SSID: ");
  tft.println(ssid);

  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 70);
  tft.print("IP: ");
  tft.println(WiFi.localIP().toString());

  // Update status bar
  displaySystemStatus("WiFi connected successfully");

  // Give time to read the info
  delay(2000);
}

void reconnect() {
  // Loop until we're reconnected to MQTT broker
  int attempts = 0;

  while (!mqttClient.connected() && attempts < 5) {
    Serial.print("Attempting MQTT connection...");
    displaySystemStatus("Connecting to MQTT...");

    // Create a client ID using the user's login
    String clientId = mqtt_client_id;
    clientId += String(random(0xffff), HEX);

    // Attempt to connect
    if (mqttClient.connect(clientId.c_str())) {
      Serial.println("connected");
      displaySystemStatus("MQTT connected");
      // Subscribe to message topics (both standardized and legacy topics)
      mqttClient.subscribe(mqtt_topic_messages);
      mqttClient.subscribe(mqtt_topic_legacy_messages);

      // Also subscribe to system notifications for ping messages
      mqttClient.subscribe(MQTT_TOPIC_NOTIFICATIONS);

      Serial.println("Subscribed to topics:");
      Serial.println(mqtt_topic_messages);
      Serial.println(mqtt_topic_legacy_messages);
      Serial.println(MQTT_TOPIC_NOTIFICATIONS);

      // Display a brief confirmation in message area - preserve gold accent
      tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP, tft.width() - ACCENT_WIDTH, 40, COLOR_MESSAGE_BG);

      // Ensure gold accent is intact
      drawGoldAccent();

      tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
      tft.setTextSize(2);
      tft.setTextColor(COLOR_ACCENT);
      tft.println("MQTT Connected");
      delay(1000);

      // Clear message but preserve gold accent
      tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP, tft.width() - ACCENT_WIDTH, 40, COLOR_MESSAGE_BG);
      drawGoldAccent();
    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" try again in 5 seconds");
      displaySystemStatus("MQTT connection failed. Retrying...");
      delay(5000);
      attempts++;
    }
  }

  if (!mqttClient.connected()) {
    displaySystemStatus("Failed to connect to MQTT after multiple attempts");
  }
}

void drawNULogo(int centerX, int centerY, int size) {
  // Draw a simplified NU logo
  int circleSize = size;
  int innerSize1 = size * 0.8;
  int innerSize2 = size * 0.6;
  int innerSize3 = size * 0.4;

  // Outer gold circle
  tft.fillCircle(centerX, centerY, circleSize, NU_GOLD);

  // Inner blue circle
  tft.fillCircle(centerX, centerY, innerSize1, NU_DARKBLUE);

  // White middle ring
  tft.fillCircle(centerX, centerY, innerSize2, ST77XX_WHITE);

  // Blue inner circle
  tft.fillCircle(centerX, centerY, innerSize3, NU_BLUE);

  // Add "NU" text in the center
  tft.setTextColor(NU_GOLD);
  tft.setTextSize(1);

  // Center the text in the logo
  tft.setCursor(centerX - 5, centerY - 3);
  tft.print("NU");
}

void setup() {
  Serial.begin(115200);
  Serial.println("Starting National University Philippines Desk Unit");
  Serial.print("Current user: ");
  Serial.println(current_user);
  Serial.print("Current time: ");
  Serial.println(current_date_time);

  // Initialize MQTT topics with faculty ID - both standardized and legacy
  sprintf(mqtt_topic_messages, MQTT_TOPIC_REQUESTS, FACULTY_ID);
  sprintf(mqtt_topic_status, MQTT_TOPIC_STATUS, FACULTY_ID);
  sprintf(mqtt_topic_mac_status, MQTT_TOPIC_MAC_STATUS, FACULTY_ID);
  strcpy(mqtt_topic_legacy_messages, MQTT_LEGACY_MESSAGES);
  strcpy(mqtt_topic_legacy_status, MQTT_LEGACY_STATUS);
  sprintf(mqtt_client_id, "DeskUnit_%s", FACULTY_NAME);

  Serial.println("MQTT topics initialized:");
  Serial.print("Standard messages topic: ");
  Serial.println(mqtt_topic_messages);
  Serial.print("Standard status topic: ");
  Serial.println(mqtt_topic_status);
  Serial.print("Legacy messages topic: ");
  Serial.println(mqtt_topic_legacy_messages);
  Serial.print("Legacy status topic: ");
  Serial.println(mqtt_topic_legacy_status);
  Serial.print("Client ID: ");
  Serial.println(mqtt_client_id);

  // Initialize SPI communication
  SPI.begin();

  // Initialize ST7789 display (240x320)
  tft.init(240, 320); // Initialize the display with its dimensions

  Serial.println("Display initialized");

  // Set rotation for landscape orientation
  tft.setRotation(1);

  // Run screen test to check if display is working correctly
  testScreen();

  // Setup the basic UI framework - truly seamless design
  drawUIFramework();

  // Show current time/date in header
  updateTimeDisplay();

  // Show initial status
  displaySystemStatus("Initializing system...");

  // Display welcome message with NU branding - seamlessly
  int centerX = tft.width() / 2;
  int logoY = MESSAGE_AREA_TOP + 60;

  // Ensure gold accent is intact
  drawGoldAccent();

  // Draw NU logo
  drawNULogo(centerX, logoY, 35);

  // Draw welcome text
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
  tft.setTextColor(NU_GOLD);
  tft.setTextSize(2);
  tft.println("Welcome, " + String(current_user));

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 50);
  tft.setTextSize(1.5);
  tft.setTextColor(ST77XX_WHITE);
  tft.println("National University");

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 70);
  tft.println("Professor's Desk Unit");

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 100);
  tft.setTextColor(NU_GOLD);
  tft.println("System Initializing...");

  delay(2000);

  // Set up WiFi - this will clear the welcome message but preserve gold accent
  setup_wifi();

  // Initialize NTP time synchronization after WiFi is connected
  initializeNTP();

  // Configure time (legacy ESP32 time configuration as fallback)
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer1);

  // Initialize MQTT
  mqttClient.setServer(mqtt_server, mqtt_port);
  mqttClient.setCallback(callback);

  // Initialize BLE Scanner for MAC address detection
  Serial.println("Initializing MAC address-based faculty detection...");
  initializeBLEScanner();

  // Initialize MAC detection variables
  facultyPresent = false;
  oldFacultyPresent = false;
  lastMacDetectionTime = 0;
  lastBleScanTime = 0;
  macDetectionCount = 0;
  macAbsenceCount = 0;
  detectedFacultyMac = "";
  lastDetectedMac = "";

  // Publish initial status to MQTT (both standardized and legacy topics)
  mqttClient.publish(mqtt_topic_status, "keychain_disconnected");
  mqttClient.publish(mqtt_topic_legacy_status, "keychain_disconnected");
  Serial.println("MAC address detection ready, scanning for faculty devices");
  displaySystemStatus("Scanning for faculty...");

  // Update time display
  updateTimeDisplay();

  // Display ready message - seamless design (preserve gold accent)
  tft.fillRect(ACCENT_WIDTH, MESSAGE_AREA_TOP, tft.width() - ACCENT_WIDTH, tft.height() - MESSAGE_AREA_TOP - STATUS_HEIGHT, COLOR_MESSAGE_BG);

  // Ensure gold accent is maintained
  drawGoldAccent();

  // Redraw NU logo
  drawNULogo(centerX, logoY, 35);

  // Text
  tft.setCursor(ACCENT_WIDTH + 5, MESSAGE_AREA_TOP + 10);
  tft.setTextColor(NU_GOLD);
  tft.setTextSize(2);
  tft.println("System Ready");

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 50);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextSize(1.5);
  tft.println("National University");

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 70);
  tft.println("Professor's Desk Unit");

  tft.setCursor(ACCENT_WIDTH + 5, logoY + 100);
  tft.setTextColor(NU_LIGHTGOLD);
  tft.println("Waiting for messages...");
}

void loop() {
  // MQTT connection management with improved reliability
  static unsigned long lastMqttCheckTime = 0;
  unsigned long currentMillis = millis();

  // Check MQTT connection every 5 seconds
  if (!mqttClient.connected() || (currentMillis - lastMqttCheckTime > 5000)) {
    lastMqttCheckTime = currentMillis;

    if (!mqttClient.connected()) {
      Serial.println("MQTT disconnected, attempting to reconnect...");
      reconnect();
    } else {
      // Periodically check if we're still receiving messages by pinging the broker
      mqttClient.publish(MQTT_TOPIC_NOTIFICATIONS, "ping");
    }
  }

  // Process MQTT messages
  mqttClient.loop();

  // Perform BLE scanning for faculty MAC addresses
  performBLEScan();

  // Periodic status updates with improved efficiency
  if (currentMillis - lastStatusUpdate > 300000) { // Every 5 minutes
    lastStatusUpdate = currentMillis;

    // Determine status message based on faculty presence
    const char* status_message;

    if (facultyPresent) {
      status_message = "keychain_connected";
      Serial.println("Periodic faculty present status update sent");
    } else {
      status_message = "keychain_disconnected";
      Serial.println("Periodic faculty absent status update sent");
    }

    // Send to both standardized and legacy topics
    mqttClient.publish(mqtt_topic_status, status_message);
    mqttClient.publish(mqtt_topic_legacy_status, status_message);

    // Also update the display status
    if (facultyPresent) {
      displaySystemStatus("Faculty Present");
    } else {
      displaySystemStatus("Faculty Absent");
    }
  }

  // Update time display every minute
  if (currentMillis - lastTimeUpdate > 60000) {
    lastTimeUpdate = currentMillis;
    updateTimeDisplay();
  }

  // Check for periodic NTP time synchronization
  checkPeriodicTimeSync();
}